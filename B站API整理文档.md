# B站API整理文档

## 📋 目录
- [登录鉴权相关](#登录鉴权相关)
  - [二维码登录](#二维码登录)
  - [密码登录](#密码登录)
  - [短信登录](#短信登录)
  - [登录状态管理](#登录状态管理)
- [用户信息获取](#用户信息获取)
- [直播间状态信息](#直播间状态信息)
- [直播间管理操作](#直播间管理操作)
- [直播间弹幕监控](#直播间弹幕监控)
- [发送消息相关](#发送消息相关)
- [直播间礼物相关](#直播间礼物相关)
- [直播间用户相关](#直播间用户相关)

---

## 🔐 登录鉴权相关

### 二维码登录

#### 1. 申请二维码(web端)
**接口地址：** `https://passport.bilibili.com/x/passport-login/web/qrcode/generate`
**请求方式：** GET
**认证方式：** 无需认证
**密钥超时：** 180秒

**响应数据：**
```json
{
  "code": 0,
  "message": "0",
  "ttl": 1,
  "data": {
    "url": "二维码内容(登录页面url)",
    "qrcode_key": "扫码登录秘钥(恒为32字符)"
  }
}
```

#### 2. 扫码登录检测(web端)
**接口地址：** `https://passport.bilibili.com/x/passport-login/web/qrcode/poll`
**请求方式：** GET
**认证方式：** 无需认证

**URL参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| qrcode_key | str | 扫码登录秘钥 | 必要 | 从申请二维码接口获取 |

**响应状态码：**
- `86101`: 未扫码
- `86090`: 二维码已失效
- `86038`: 二维码已失效
- `0`: 登录成功

**成功登录后会设置Cookie：**
`DedeUserID` `DedeUserID__ckMd5` `SESSDATA` `bili_jct`

### 密码登录

#### 1. 获取公钥&盐(web端)
**接口地址：** `https://passport.bilibili.com/x/passport-login/web/key`
**请求方式：** GET
**认证方式：** 无需认证

**响应数据：**
```json
{
  "code": 0,
  "message": "0",
  "ttl": 1,
  "data": {
    "hash": "密码盐值(16字符)",
    "key": "rsa公钥(PEM格式)"
  }
}
```

#### 2. 申请captcha验证码
**接口地址：** `https://passport.bilibili.com/x/passport-login/captcha?source=main_web`
**请求方式：** GET
**认证方式：** 无需认证

#### 3. 密码登录操作(web端)
**接口地址：** `https://passport.bilibili.com/x/passport-login/web/login`
**请求方式：** POST
**认证方式：** 无需认证

**正文参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| username | str | 用户登录账号 | 必要 | 手机号或邮箱地址 |
| password | str | 加密后的带盐密码 | 必要 | base64格式 |
| keep | num | 0 | 必要 | |
| token | str | 登录token | 必要 | 从captcha接口获取 |
| challenge | str | 极验challenge | 必要 | 从captcha接口获取 |
| validate | str | 极验result | 必要 | 极验验证后得到 |
| seccode | str | 极验result+`\|jordan` | 必要 | 极验验证后得到 |
| go_url | str | 跳转url | 非必要 | 默认为https://www.bilibili.com |
| source | str | 登录来源 | 非必要 | main_web:独立登录页 main_mini:小窗登录 |

### 短信登录

#### 1. 获取国际冠字码
**接口地址：** `https://passport.bilibili.com/web/generic/country/list`
**请求方式：** GET
**认证方式：** 无需认证

**响应数据：**
```json
{
  "code": 0,
  "data": {
    "common": [
      {
        "id": 1,
        "cname": "中国大陆",
        "country_id": "86"
      }
    ],
    "others": [...]
  }
}
```

#### 2. 发送短信验证码
**接口地址：** `https://passport.bilibili.com/x/passport-login/sms/send`
**请求方式：** POST
**认证方式：** 无需认证
**CD时间：** 60秒

**正文参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| cid | num | 国际冠字码 | 必要 | 如86(中国大陆) |
| tel | num | 手机号码 | 必要 | |
| source | str | 登录来源 | 必要 | main_web |
| token | str | 登录token | 必要 | 从captcha接口获取 |
| challenge | str | 极验challenge | 必要 | 从captcha接口获取 |
| validate | str | 极验result | 必要 | 极验验证后得到 |
| seccode | str | 极验result+`\|jordan` | 必要 | 极验验证后得到 |

#### 3. 短信验证登录
**接口地址：** `https://passport.bilibili.com/x/passport-login/login/sms`
**请求方式：** POST
**认证方式：** 无需认证

**正文参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| cid | num | 国际冠字码 | 必要 | |
| tel | num | 手机号码 | 必要 | |
| code | num | 短信验证码 | 必要 | |
| captcha_key | str | 验证码key | 必要 | |
| login_session_id | str | 登录会话id | 必要 | |

### 登录状态管理

#### 1. 检查是否需要刷新Cookie
**接口地址：** `https://passport.bilibili.com/x/passport-login/web/cookie/info`
**请求方式：** GET
**认证方式：** Cookie(SESSDATA)

**URL参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| csrf | str | CSRF Token | 非必要 | 位于Cookie中的bili_jct字段 |

#### 2. 查询登录记录
**接口地址：** `https://api.bilibili.com/x/safecenter/login_notice`
**请求方式：** GET
**认证方式：** Cookie(SESSDATA)

**URL参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| mid | num | 用户mid | 必要 | 必须为自己的mid |
| buvid | str | 设备虚拟id | 非必要 | web端为Cookie中的buvid3 |

#### 3. 最近一周的登录情况
**接口地址：** `https://api.bilibili.com/x/member/web/login/log`
**请求方式：** GET
**认证方式：** Cookie(SESSDATA)

#### 4. 退出登录(web端)
**接口地址：** `https://passport.bilibili.com/login/exit/v2`
**请求方式：** POST
**认证方式：** Cookie

**正文参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| biliCSRF | str | CSRF Token | 必要 | 位于cookie中的bili_jct |
| gourl | str | 成功后跳转到的页面 | 非必要 | 默认为javascript:history.go(-1) |

---

## 👤 用户信息获取

### 1. 导航栏用户信息
**接口地址：** `https://api.bilibili.com/x/web-interface/nav`
**请求方式：** GET
**认证方式：** Cookie（SESSDATA）

**响应数据：**
```json
{
  "code": 0,
  "message": "0",
  "ttl": 1,
  "data": {
    "isLogin": true,
    "email_verified": 1,
    "face": "用户头像url",
    "uname": "用户名",
    "mid": 用户mid,
    "mobile_verified": 1,
    "money": 硬币数,
    "moral": 节操值,
    "official": {
      "role": 认证类型,
      "title": "认证信息",
      "desc": "认证描述"
    },
    "pendant": {
      "pid": 挂件id,
      "name": "挂件名称",
      "image": "挂件图片url"
    },
    "scores": 当前等级积分,
    "vip": {
      "type": 会员类型,
      "status": 会员状态,
      "due_date": 会员到期时间戳,
      "vip_pay_type": 会员开通方式
    },
    "wallet": {
      "mid": 用户mid,
      "bcoin_balance": B币余额,
      "coupon_balance": 优惠券余额
    },
    "has_shop": false,
    "shop_url": "",
    "allowance_count": 0,
    "answer_status": 0
  }
}
```

### 2. 登录用户状态数
**接口地址：** `https://api.bilibili.com/x/web-interface/nav/stat`
**请求方式：** GET
**认证方式：** Cookie（SESSDATA）或APP

**URL参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| access_key | str | APP登录Token | APP方式必要 | |

**响应数据：**
```json
{
  "code": 0,
  "message": "0",
  "ttl": 1,
  "data": {
    "following": 754,
    "follower": 596,
    "dynamic_count": 252
  }
}
```

### 3. 用户空间详细信息
**接口地址：** `https://api.bilibili.com/x/space/wbi/acc/info`
**请求方式：** GET
**认证方式：** Cookie（SESSDATA）
**鉴权方式：** Wbi 签名

**URL参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| mid | num | 目标用户mid | 必要 | |
| w_rid | str | Wbi 签名 | 必要 | 详见 Wbi 签名文档 |
| wts | num | 当前时间戳 | 必要 | 详见 Wbi 签名文档 |

### 4. 个人中心信息
**接口地址：** `https://api.bilibili.com/x/member/web/account`
**请求方式：** GET
**认证方式：** Cookie（SESSDATA）或APP

### 5. 查询大会员状态
**接口地址：** `https://api.bilibili.com/x/vip/web/user/info`
**请求方式：** GET
**认证方式：** Cookie（SESSDATA）

### 6. 获取历史记录列表
**接口地址：** `https://api.bilibili.com/x/web-interface/history/cursor`
**请求方式：** GET
**认证方式：** Cookie(SESSDATA)

**URL参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| max | num | 历史记录截止时间 | 非必要 | 时间戳，默认为当前时间 |
| view_at | num | 历史记录查看时间 | 非必要 | 时间戳 |
| business | str | 历史记录业务类型 | 非必要 | archive:稿件 live:直播 article-list:文集 |
| ps | num | 每页项数 | 非必要 | 默认20，最大30 |

---

## 🎬 直播间状态信息

### 1. 直播间基本信息
**接口地址：** `https://api.live.bilibili.com/room/v1/Room/room_init`
**请求方式：** GET
**认证方式：** 无需认证

**URL参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| id | num | 直播间id | 必要 | 可以是短号或长号 |

**响应数据：**
```json
{
  "code": 0,
  "msg": "ok",
  "message": "ok",
  "data": {
    "room_id": 直播间真实id,
    "short_id": 直播间短号,
    "uid": 主播mid,
    "need_p2p": 0,
    "is_hidden": false,
    "is_locked": false,
    "is_portrait": false,
    "live_status": 直播状态,
    "hidden_till": 隐藏时间戳,
    "lock_till": 锁定时间戳,
    "encrypted": false,
    "pwd_verified": false,
    "live_time": 开播时间,
    "room_shield": 0,
    "is_sp": 是否为特殊直播间,
    "special_type": 特殊直播间标志
  }
}
```

**live_status 状态说明：**
- `0`: 未开播
- `1`: 直播中
- `2`: 轮播中

### 2. 获取用户对应的直播间状态
**接口地址：** `https://api.live.bilibili.com/room/v1/Room/getRoomInfoOld`
**请求方式：** GET
**认证方式：** 无需认证

**URL参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| mid | num | 目标用户mid | 必要 | |

**响应数据：**
```json
{
  "code": 0,
  "message": "0",
  "ttl": 1,
  "data": {
    "roomStatus": 直播间状态,
    "roundStatus": 轮播状态,
    "live_status": 直播状态,
    "url": "直播间网页url",
    "title": "直播间标题",
    "cover": "直播间封面url",
    "online": 直播间人气,
    "roomid": 直播间id短号,
    "broadcast_type": 0,
    "online_hidden": 0
  }
}
```

### 3. 批量查询直播间状态
**接口地址：** `https://api.live.bilibili.com/room/v1/Room/get_status_info_by_uids`
**请求方式：** GET/POST
**认证方式：** 无需认证

**GET方式URL参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| uids[] | array | 要查询的主播mid | 必要 | |

**POST方式正文参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| uids | nums | 要查询的主播mid | 必要 | |

### 4. 获取直播间基本信息(新版)
**接口地址：** `https://api.live.bilibili.com/xlive/web-room/v1/index/getRoomBaseInfo`
**请求方式：** GET
**认证方式：** 无需认证
**注：** 亦可用于批量获取

**URL参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| room_ids | str | 直播间id | 必要 | 多个用逗号分隔 |
| req_biz | str | 请求业务 | 非必要 | |

### 5. 获取主播信息
**接口地址：** `https://api.live.bilibili.com/live_user/v1/Master/info`
**请求方式：** GET
**认证方式：** 无需认证

**URL参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| uid | num | 主播mid | 必要 | |

### 6. 获取直播间最近弹幕
**接口地址：** `https://api.live.bilibili.com/xlive/web-room/v1/dM/gethistory`
**请求方式：** GET
**认证方式：** 无需认证

**URL参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| roomid | num | 直播间短ID | 必要 | |

---

## 🎮 直播间管理操作

### 1. 开通直播间
**接口地址：** `https://api.live.bilibili.com/xlive/app-blink/v1/preLive/CreateRoom`
**请求方式：** POST
**认证方式：** Cookie（SESSDATA）
**鉴权方式：** Cookie中`bili_jct`的值正确并与`csrf`相同

**正文参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| platform | str | 客户端 | 必要 | 默认值web |
| visit_id | str | 未知 | 非必要 | 默认空 |
| csrf | str | CSRF Token | 必要 | 位于cookie |
| csrf_token | str | CSRF Token | 非必要 | 位于cookie |

**响应数据：**
```json
{
  "code": 0,
  "message": "0",
  "ttl": 1,
  "data": {
    "roomID": "1234"
  }
}
```

### 2. 开始直播
**接口地址：** `https://api.live.bilibili.com/room/v1/Room/startLive`
**请求方式：** POST
**认证方式：** Cookie（SESSDATA）
**鉴权方式：** Cookie中`bili_jct`的值正确并与`csrf`相同

**正文参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| room_id | num | 直播间id | 必要 | |
| area_v2 | num | 直播分区id | 必要 | |
| platform | str | 直播平台 | 必要 | web/pc |
| csrf | str | CSRF Token | 必要 | |

**响应数据包含推流地址和推流参数：**
```json
{
  "code": 0,
  "msg": "ok",
  "message": "ok",
  "data": {
    "change": 1,
    "status": "LIVE",
    "room_type": 0,
    "rtmp": [
      {
        "protocol": "rtmp",
        "addr": "rtmp://live-push.bilivideo.com/live-bvc/",
        "code": "?streamname=live_xxx&key=xxx",
        "new_link": "推流链接",
        "provider": "txy"
      }
    ]
  }
}
```

### 3. 关闭直播
**接口地址：** `https://api.live.bilibili.com/room/v1/Room/stopLive`
**请求方式：** POST
**认证方式：** Cookie（SESSDATA）
**鉴权方式：** Cookie中`bili_jct`的值正确并与`csrf`相同

**正文参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| platform | str | 直播平台 | 必要 | pc_link:直播姬PC android_link:直播姬安卓 |
| room_id | num | 直播间id | 必要 | 必须为自己的直播间id |
| csrf | str | CSRF Token | 必要 | |

**响应数据：**
```json
{
  "code": 0,
  "msg": "",
  "message": "",
  "data": {
    "change": 1,
    "status": "PREPARING"
  }
}
```

### 4. 更新直播间信息
**接口地址：** `https://api.live.bilibili.com/room/v1/Room/update`
**请求方式：** POST
**认证方式：** Cookie（SESSDATA）
**鉴权方式：** Cookie中`bili_jct`的值正确并与`csrf`相同

**正文参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| room_id | num | 直播间id | 必要 | |
| title | str | 直播间标题 | 非必要 | |
| area_id | num | 直播分区id | 非必要 | |
| csrf | str | CSRF Token | 必要 | |

### 5. 预更新直播间信息
**接口地址：** `https://api.live.bilibili.com/xlive/app-blink/v1/preLive/UpdatePreLiveInfo`
**请求方式：** POST
**认证方式：** Cookie（SESSDATA）
**鉴权方式：** Cookie中`bili_jct`的值正确并与`csrf`相同

**正文参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| room_id | num | 直播间id | 必要 | |
| title | str | 直播间标题 | 非必要 | |
| area_v2 | num | 直播分区id | 非必要 | |
| csrf | str | CSRF Token | 必要 | |

---

## 📺 直播间弹幕监控

### 1. 获取信息流认证秘钥
**接口地址：** `https://api.live.bilibili.com/xlive/web-room/v1/index/getDanmuInfo`
**请求方式：** GET
**认证方式：** Cookie(SESSDATA)
**鉴权方式：** Wbi 签名, Cookie中的`buvid3`不为空

**URL参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| id | num | 直播间真实id | 必要 | |
| type | num | (?) | 非必要 | 作用尚不明确 |
| web_location | str | (?) | 非必要 | 作用尚不明确 |
| w_rid | str | Wbi 签名 | 必要 | 详见 Wbi 签名文档 |
| wts | num | 当前时间戳 | 必要 | 详见 Wbi 签名文档 |

**响应数据：**
```json
{
  "code": 0,
  "message": "0",
  "ttl": 1,
  "data": {
    "group": "live",
    "business_id": 0,
    "refresh_row_factor": 0.125,
    "refresh_rate": 100,
    "max_delay": 5000,
    "token": "认证token",
    "host_list": [
      {
        "host": "服务器域名",
        "port": TCP端口,
        "wss_port": WSS端口,
        "ws_port": WS端口
      }
    ]
  }
}
```

### 2. WebSocket连接格式
**连接地址：** `wss://broadcastlv.chat.bilibili.com:443/sub`
**协议：** WebSocket

**数据包格式：** 头部数据 + 正文数据

**操作流程：**
1. 建立WebSocket连接
2. 发送认证包
3. 接收认证回复
4. 定时发送心跳包(30秒间隔)
5. 接收普通数据包

**主要消息类型：**
- `DANMU_MSG`: 弹幕消息
- `SEND_GIFT`: 礼物消息
- `COMBO_SEND`: 连击礼物
- `GUARD_BUY`: 上舰消息
- `USER_TOAST_MSG`: 续费消息
- `INTERACT_WORD`: 用户进入直播间
- `ONLINE_RANK_COUNT`: 高能榜数量
- `ONLINE_RANK_V2`: 高能榜变化
- `STOP_LIVE_ROOM_LIST`: 停止直播
- `PREPARING`: 直播准备中
- `LIVE`: 开始直播
- `ROOM_REAL_TIME_MESSAGE_UPDATE`: 粉丝数等更新

### 3. 弹幕消息结构(DANMU_MSG)
**弹幕消息包含以下主要信息：**
```json
{
  "cmd": "DANMU_MSG",
  "info": [
    [弹幕出现时间, 弹幕类型, 字体大小, 颜色, 时间戳, 弹幕池, 用户hash, 弹幕id],
    "弹幕内容",
    [用户mid, "用户名", 是否房管, 是否月费老爷, 是否年费老爷, 用户等级, 用户等级颜色, "用户头衔"],
    [粉丝勋章等级, "粉丝勋章名", "主播名", 房间号, 勋章颜色],
    [用户等级, 0, 用户等级颜色, "用户等级"],
    ["", ""],
    0,
    0,
    null,
    {
      "ts": 时间戳,
      "ct": "弹幕ct"
    },
    0,
    0,
    null,
    null,
    0,
    弹幕模式信息
  ]
}
```

### 4. 礼物消息结构(SEND_GIFT)
**礼物消息包含以下主要信息：**
```json
{
  "cmd": "SEND_GIFT",
  "data": {
    "giftName": "礼物名称",
    "num": 礼物数量,
    "uname": "用户名",
    "face": "用户头像",
    "guard_level": 舰长等级,
    "rcost": 礼物价值,
    "uid": 用户mid,
    "timestamp": 时间戳,
    "giftId": 礼物id,
    "giftType": 礼物类型,
    "action": "投喂",
    "super": 0,
    "price": 礼物单价,
    "rnd": "随机数",
    "newMedal": 0,
    "newTitle": 0,
    "medal": [勋章等级, "勋章名", "主播名", 房间号, 勋章颜色],
    "title": ["", ""],
    "beatId": "",
    "biz_source": "live",
    "metadata": "",
    "remain": 0,
    "gold": 0,
    "silver": 0,
    "eventScore": 0,
    "eventNum": 0,
    "smalltv_msg": [],
    "specialGift": null,
    "notice_msg": [],
    "capsule": null,
    "addFollow": 0,
    "effect_block": 1,
    "coin_type": "silver",
    "total_coin": 0,
    "effect": 0,
    "tag_image": "",
    "user_count": 0
  }
}
```

---

## 💬 发送消息相关

### 1. 发送直播弹幕
**接口地址：** `https://api.live.bilibili.com/msg/send`
**请求方式：** POST
**认证方式：** Cookie (SESSDATA)
**鉴权方式：** Cookie中`bili_jct`的值正确并与`csrf`相同

**URL参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| w_rid | str | wbi签名 | 非必要 | 不强制需要 |
| wts | num | Unix 秒时间戳 | 非必要 | 不强制需要 |

**正文参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| csrf | str | CSRF Token | 必要 | 位于cookie |
| roomid | num | 直播间id | 必要 | |
| msg | str | 弹幕内容 | 必要 | |
| rnd | num | 当前 Unix 秒时间戳 | 必要 | |
| fontsize | num | 字体大小 | 必要 | 默认为25 |
| color | num | 十进制颜色值 | 必要 | 实际无效果 |
| mode | num | 展示模式 | 非必要 | 默认为1 |
| bubble | num | (?) | 非必要 | 值为0 |
| room_type | num | (?) | 非必要 | 0 |
| jumpfrom | num | (?) | 非必要 | 0 |
| reply_mid | num | 要"@"的用户mid | 非必要 | 默认为0 |
| reply_attr | num | (?) | 非必要 | 0 |
| reply_uname | str | 要"@"的用户名称 | 非必要 | 默认为"" |
| replay_dmid | str | 要回复的弹幕id | 非必要 | 默认为"" |
| statistics | str | (?) | 非必要 | {"appId":100,"platform":5} |
| csrf_token | str | 同csrf | 非必要 | |

**响应状态码：**
- `0`: 成功
- `-101`: 账号未登录
- `-111`: csrf 校验失败
- `-400`: 请求错误
- `1003212`: 超出限制长度
- `10031`: 发送频率过快

### 2. 获取当前用户对应直播间可发弹幕配置
**接口地址：** `https://api.live.bilibili.com/xlive/web-room/v1/dM/GetDMConfigByGroup`
**请求方式：** GET
**认证方式：** Cookie (SESSDATA)

**URL参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| room_id | num | 直播间id | 必要 | |
| web_location | str | (?) | 非必要 | 作用尚不明确 |
| w_rid | str | wbi签名 | 非必要 | 不强制需要 |
| wts | num | 秒时间戳 | 非必要 | 不强制需要 |

### 3. 设置直播间弹幕配置
**接口地址：** `https://api.live.bilibili.com/xlive/web-room/v1/dM/AjaxSetConfig`
**请求方式：** POST
**认证方式：** Cookie (SESSDATA)
**鉴权方式：** Cookie中`bili_jct`的值正确并与`csrf`相同

**正文参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| room_id | num | 直播间id | 必要 | |
| color | str | 弹幕颜色 | 非必要 | 十六进制颜色值，如0xffffff |
| mode | num | 弹幕模式 | 非必要 | |
| csrf | str | CSRF Token | 必要 | |

### 4. 发送视频弹幕
**接口地址：** `https://api.bilibili.com/x/v2/dm/post`
**请求方式：** POST
**认证方式：** Cookie（SESSDATA）或APP

**URL参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| web_location | str | 普通视频: 1315873 | 不必要 | |
| csrf | str | CSRF Token | Cookie方式必要 | 即Cookie中bili_jct |
| w_rid | str | Wbi 签名 | 必要 | 参见 Wbi 签名文档 |
| wts | str | UNIX 秒级时间戳 | 必要 | 参见 Wbi 签名文档 |

**正文参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| type | num | 弹幕类型 | 必要 | 1:视频弹幕 2:漫画弹幕 |
| oid | num | 弹幕所属对象id | 必要 | 视频:cid 漫画:漫画id |
| msg | str | 弹幕内容 | 必要 | |
| aid | num | 稿件avid | 必要 | |
| progress | num | 弹幕出现时间 | 必要 | 毫秒 |
| color | num | 弹幕颜色 | 必要 | 十进制RGB888值 |
| fontsize | num | 弹幕字号 | 必要 | 12:非常小 16:特小 18:小 25:中 36:大 45:很大 64:特别大 |
| pool | num | 弹幕池 | 必要 | 0:普通池 1:字幕池 2:特殊池 |
| mode | num | 弹幕类型 | 必要 | 1-3:普通弹幕 4:底部弹幕 5:顶部弹幕 6:逆向弹幕 7:高级弹幕 8:代码弹幕 9:BAS弹幕 |
| rnd | num | 时间戳 | 必要 | 发送时的时间戳 |
| plat | num | 平台 | 必要 | 1:web端 |

### 5. 发送私信（web端）
**接口地址：** `https://api.vc.bilibili.com/web_im/v1/web_im/send_msg`
**请求方式：** POST
**认证方式：** Cookie（SESSDATA）
**鉴权方式：** Wbi 签名

**仅支持发送 `msg[msg_type]` 为 `1`、`2` 或 `5` 的私信**

**正文参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| msg[sender_uid] | num | 发送者mid | 必要 | |
| msg[receiver_id] | num | 接收者mid | 必要 | |
| msg[receiver_type] | num | 接收者类型 | 必要 | 1:用户 |
| msg[msg_type] | num | 消息类型 | 必要 | 1:文字 2:图片 5:撤回消息 |
| msg[msg_status] | num | 消息状态 | 必要 | 0 |
| msg[content] | str | 消息内容 | 必要 | JSON格式 |
| msg[timestamp] | num | 时间戳 | 必要 | |
| msg[dev_id] | str | 设备id | 必要 | |
| csrf | str | CSRF Token | 必要 | |
| csrf_token | str | 同csrf | 非必要 | |

---

## 🎁 直播间礼物相关

### 1. 获取直播间内礼物
**接口地址：** `https://api.live.bilibili.com/xlive/web-room/v1/giftPanel/roomGiftList`
**请求方式：** GET
**认证方式：** 无需认证

**URL参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| platform | str | web | 必要 | |
| room_id | num | 主播房间号 | 必要 | |
| area_parent_id | num | 直播分区 | 非必要 | 不填写可能会获取不到部分活动礼物 |
| area_id | num | 直播子分区 | 非必要 | 不填写可能会获取不到部分活动礼物 |

**响应数据包含礼物列表：**
```json
{
  "code": 0,
  "message": "0",
  "data": {
    "gift_config": {
      "base_config": {
        "list": [
          {
            "id": 礼物id,
            "name": "礼物名字",
            "price": 价格,
            "type": 类型,
            "coin_type": "gold",
            "effect": 特效类型,
            "stay_time": 展示时间,
            "animation_frame_num": 动画帧数,
            "desc": "礼物描述",
            "img_basic": "礼物图片",
            "gif": "礼物gif动画"
          }
        ]
      }
    }
  }
}
```

### 2. 获取盲盒概率
**接口地址：** `https://api.live.bilibili.com/xlive/general-interface/v1/blindFirstWin/getInfo`
**请求方式：** GET
**认证方式：** 无需认证

**URL参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| gift_id | num | 盲盒对应的礼物id | 必要 | |

**响应数据：**
```json
{
  "code": 0,
  "message": "0",
  "data": {
    "note_text": "描述",
    "blind_price": 盲盒价格,
    "blind_gift_name": "盲盒名字",
    "gifts": [
      {
        "gift_id": 爆出的礼物id,
        "price": 爆出的礼物价格,
        "gift_name": "礼物名字",
        "gift_img": "礼物图片",
        "chance": "概率"
      }
    ]
  }
}
```

---

## 👥 直播间用户相关

### 1. 获取自己持有的粉丝勋章信息
**接口地址：** `https://api.live.bilibili.com/xlive/app-ucenter/v1/user/GetMyMedals`
**请求方式：** GET
**认证方式：** Cookie（SESSDATA）或APP

**URL参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| page_size | num | 每页的数量 | 必要 | 最大为10 |
| page | num | 返回结果页数 | 必要 | |

**响应数据：**
```json
{
  "code": 0,
  "message": "0",
  "ttl": 1,
  "data": {
    "count": 勋章数量,
    "items": [
      {
        "can_delete": true,
        "day_limit": 1500,
        "guard_level": 0,
        "guard_medal_title": "未开启加成",
        "intimacy": 9617,
        "is_lighted": 0,
        "level": 11,
        "medal_name": "勋章名",
        "medal_color_border": 12632256,
        "medal_color_end": 12632256,
        "medal_color_start": 12632256,
        "medal_id": 29245,
        "next_intimacy": 10000,
        "today_feed": 0,
        "roomid": 直播间房间号,
        "status": 状态,
        "target_id": UP主mid,
        "target_name": "UP主用户名",
        "uname": "UP主用户名"
      }
    ],
    "page_info": {
      "total_page": 页码总长度,
      "cur_page": 当前返回的页码
    }
  }
}
```

### 2. 佩戴粉丝勋章
**接口地址：** `https://api.live.bilibili.com/xlive/web-room/v1/fansMedal/wear`
**请求方式：** POST
**认证方式：** Cookie（SESSDATA）
**鉴权方式：** Cookie中`bili_jct`的值正确并与`csrf`相同

**正文参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| medal_id | num | 粉丝勋章id | 必要 | |
| csrf | str | CSRF Token | 必要 | |

### 3. 取消佩戴粉丝勋章
**接口地址：** `https://api.live.bilibili.com/xlive/web-room/v1/fansMedal/take_off`
**请求方式：** POST
**认证方式：** Cookie（SESSDATA）
**鉴权方式：** Cookie中`bili_jct`的值正确并与`csrf`相同

**正文参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| csrf | str | CSRF Token | 必要 | |

### 4. 获取直播间用户列表
**接口地址：** `https://api.live.bilibili.com/xlive/web-room/v1/index/getInfoByUser`
**请求方式：** GET
**认证方式：** Cookie（SESSDATA）

**URL参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| room_id | num | 直播间id | 必要 | |

### 5. 直播间禁言用户管理
**接口地址：** `https://api.live.bilibili.com/xlive/web-room/v1/banned/GetSilentUserList`
**请求方式：** GET
**认证方式：** Cookie（SESSDATA）

**URL参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| room_id | num | 直播间id | 必要 | 必须为自己的直播间 |
| page | num | 页码 | 非必要 | 默认为1 |

---

## 📝 备注说明

### 1. 认证方式说明
- **Cookie（SESSDATA）**：需要登录后的SESSDATA cookie，用于身份验证
- **Wbi 签名**：需要按照B站Wbi签名算法生成签名，用于防刷验证
- **CSRF Token**：即Cookie中的bili_jct值，用于防止跨站请求伪造攻击
- **APP认证**：使用access_key进行APP端认证

### 2. 常用状态码
- `0`: 成功
- `-101`: 账号未登录
- `-111`: csrf校验失败
- `-400`: 请求错误
- `-404`: 无此项
- `-500`: 服务器错误
- `86101`: 二维码未扫描
- `86090`: 二维码已失效
- `86038`: 二维码已失效
- `1003212`: 超出限制长度
- `10031`: 发送频率过快
- `10064002`: 参数错误
- `10065107`: 颜色不存在

### 3. 时间戳格式
- **Unix秒级时间戳**：大部分接口使用，如登录、用户信息等
- **Unix毫秒级时间戳**：弹幕相关接口部分使用
- **格式化时间**：部分接口返回YYYY-MM-DD HH:mm:ss格式

### 4. 直播间ID说明
- **短号**：用户自定义的短直播间号，如1024
- **长号**：系统分配的真实直播间ID，如23058
- **使用建议**：大部分接口需要使用真实ID（长号）

### 5. 弹幕相关说明
- **弹幕池类型**：0:普通池 1:字幕池 2:特殊池
- **弹幕模式**：1-3:普通弹幕 4:底部弹幕 5:顶部弹幕 6:逆向弹幕 7:高级弹幕 8:代码弹幕 9:BAS弹幕
- **颜色格式**：支持十进制RGB888值和十六进制颜色值

### 6. WebSocket连接说明
- **连接地址**：wss://broadcastlv.chat.bilibili.com:443/sub
- **心跳间隔**：30秒
- **数据格式**：头部数据 + 正文数据
- **认证要求**：需要先获取认证token

### 7. 登录流程说明
- **二维码登录**：无需人机验证，适合自动化场景
- **密码登录**：需要RSA加密密码，需要人机验证
- **短信登录**：需要获取验证码，需要人机验证
- **Cookie刷新**：定期检查并刷新Cookie以保持登录状态

### 8. 安全注意事项
- 所有涉及用户操作的接口都需要CSRF Token验证
- 敏感操作建议使用HTTPS协议
- 登录凭证应妥善保管，避免泄露
- 遵守B站API使用规范，避免频繁请求

### 9. 开发建议
- 建议使用官方SDK或成熟的第三方库
- 实现适当的错误处理和重试机制
- 遵循接口调用频率限制
- 及时更新API版本以获得最新功能

---

## 🔗 相关链接

- **B站开放平台**：https://open.bilibili.com/
- **API文档项目**：https://github.com/SocialSisterYi/bilibili-API-collect
- **Wbi签名算法**：详见bilibili-API-collect项目中的misc/sign/wbi.md文档

---

*整理时间：2025-01-20*
*数据来源：bilibili-API-collect 项目文档*
*整理者：Claude 4.0 Sonnet AI助手* 🐾
