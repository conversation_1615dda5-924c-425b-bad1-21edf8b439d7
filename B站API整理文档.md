# B站API整理文档

## 📋 目录
- [二维码登录鉴权检测](#二维码登录鉴权检测)
- [用户信息获取](#用户信息获取)
- [直播间状态信息](#直播间状态信息)
- [直播间开关播](#直播间开关播)
- [直播间弹幕监控](#直播间弹幕监控)
- [发送消息相关](#发送消息相关)

---

## 🔐 二维码登录鉴权检测

### 1. 申请二维码(web端)
**接口地址：** `https://passport.bilibili.com/x/passport-login/web/qrcode/generate`  
**请求方式：** GET  
**认证方式：** 无需认证  
**密钥超时：** 180秒  

**响应数据：**
```json
{
  "code": 0,
  "message": "0",
  "ttl": 1,
  "data": {
    "url": "二维码内容(登录页面url)",
    "qrcode_key": "扫码登录秘钥(恒为32字符)"
  }
}
```

### 2. 扫码登录检测(web端)
**接口地址：** `https://passport.bilibili.com/x/passport-login/web/qrcode/poll`  
**请求方式：** GET  
**认证方式：** 无需认证  

**URL参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| qrcode_key | str | 扫码登录秘钥 | 必要 | 从申请二维码接口获取 |

**响应状态码：**
- `86101`: 未扫码
- `86090`: 二维码已失效
- `86038`: 二维码已失效
- `0`: 登录成功

**成功登录后会设置Cookie：**
`DedeUserID` `DedeUserID__ckMd5` `SESSDATA` `bili_jct`

---

## 👤 用户信息获取

### 1. 导航栏用户信息
**接口地址：** `https://api.bilibili.com/x/web-interface/nav`  
**请求方式：** GET  
**认证方式：** Cookie（SESSDATA）  

**响应数据：**
```json
{
  "code": 0,
  "message": "0",
  "ttl": 1,
  "data": {
    "isLogin": true,
    "email_verified": 1,
    "face": "用户头像url",
    "uname": "用户名",
    "mid": 用户mid,
    "mobile_verified": 1,
    "money": 硬币数,
    "moral": 节操值,
    "official": {
      "role": 认证类型,
      "title": "认证信息",
      "desc": "认证描述"
    },
    "pendant": {
      "pid": 挂件id,
      "name": "挂件名称",
      "image": "挂件图片url"
    },
    "scores": 当前等级积分,
    "vip": {
      "type": 会员类型,
      "status": 会员状态,
      "due_date": 会员到期时间戳,
      "vip_pay_type": 会员开通方式
    },
    "wallet": {
      "mid": 用户mid,
      "bcoin_balance": B币余额,
      "coupon_balance": 优惠券余额
    },
    "has_shop": false,
    "shop_url": "",
    "allowance_count": 0,
    "answer_status": 0
  }
}
```

### 2. 用户空间详细信息
**接口地址：** `https://api.bilibili.com/x/space/wbi/acc/info`  
**请求方式：** GET  
**认证方式：** Cookie（SESSDATA）  
**鉴权方式：** Wbi 签名  

**URL参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| mid | num | 目标用户mid | 必要 | |
| w_rid | str | Wbi 签名 | 必要 | 详见 Wbi 签名文档 |
| wts | num | 当前时间戳 | 必要 | 详见 Wbi 签名文档 |

### 3. 个人中心信息
**接口地址：** `https://api.bilibili.com/x/member/web/account`  
**请求方式：** GET  
**认证方式：** Cookie（SESSDATA）或APP  

---

## 🎬 直播间状态信息

### 1. 直播间基本信息
**接口地址：** `https://api.live.bilibili.com/room/v1/Room/room_init`  
**请求方式：** GET  
**认证方式：** 无需认证  

**URL参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| id | num | 直播间id | 必要 | 可以是短号或长号 |

**响应数据：**
```json
{
  "code": 0,
  "msg": "ok",
  "message": "ok",
  "data": {
    "room_id": 直播间真实id,
    "short_id": 直播间短号,
    "uid": 主播mid,
    "need_p2p": 0,
    "is_hidden": false,
    "is_locked": false,
    "is_portrait": false,
    "live_status": 直播状态,
    "hidden_till": 隐藏时间戳,
    "lock_till": 锁定时间戳,
    "encrypted": false,
    "pwd_verified": false,
    "live_time": 开播时间,
    "room_shield": 0,
    "is_sp": 是否为特殊直播间,
    "special_type": 特殊直播间标志
  }
}
```

**live_status 状态说明：**
- `0`: 未开播
- `1`: 直播中
- `2`: 轮播中

### 2. 获取用户对应的直播间状态
**接口地址：** `https://api.live.bilibili.com/room/v1/Room/getRoomInfoOld`  
**请求方式：** GET  

**URL参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| mid | num | 目标用户mid | 必要 | |

### 3. 批量查询直播间状态
**接口地址：** `https://api.live.bilibili.com/room/v1/Room/get_status_info_by_uids`  
**请求方式：** GET/POST  
**认证方式：** 无需认证  

**GET方式URL参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| uids[] | array | 要查询的主播mid | 必要 | |

**POST方式正文参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| uids | nums | 要查询的主播mid | 必要 | |

### 4. 获取直播间基本信息(新版)
**接口地址：** `https://api.live.bilibili.com/xlive/web-room/v1/index/getRoomBaseInfo`  
**请求方式：** GET  
**注：** 亦可用于批量获取  

---

## 🎮 直播间开关播

### 1. 开通直播间
**接口地址：** `https://api.live.bilibili.com/xlive/app-blink/v1/preLive/CreateRoom`  
**请求方式：** POST  
**认证方式：** Cookie（SESSDATA）  
**鉴权方式：** Cookie中`bili_jct`的值正确并与`csrf`相同  

**正文参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| platform | str | 客户端 | 必要 | 默认值web |
| visit_id | str | 未知 | 非必要 | 默认空 |
| csrf | str | CSRF Token | 必要 | 位于cookie |
| csrf_token | str | CSRF Token | 非必要 | 位于cookie |

### 2. 开始直播
**接口地址：** `https://api.live.bilibili.com/room/v1/Room/startLive`  
**请求方式：** POST  
**认证方式：** Cookie（SESSDATA）  
**鉴权方式：** Cookie中`bili_jct`的值正确并与`csrf`相同  

**正文参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| room_id | num | 直播间id | 必要 | |
| area_v2 | num | 直播分区id | 必要 | |
| platform | str | 直播平台 | 必要 | web/pc |
| csrf | str | CSRF Token | 必要 | |

**响应数据包含推流地址和推流参数**

### 3. 停止直播
**接口地址：** `https://api.live.bilibili.com/room/v1/Room/stopLive`  
**请求方式：** POST  
**认证方式：** Cookie（SESSDATA）  
**鉴权方式：** Cookie中`bili_jct`的值正确并与`csrf`相同  

**正文参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| room_id | num | 直播间id | 必要 | |
| csrf | str | CSRF Token | 必要 | |

### 4. 更新直播间信息
**接口地址：** `https://api.live.bilibili.com/room/v1/Room/update`  
**请求方式：** POST  
**认证方式：** Cookie（SESSDATA）  

**正文参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| room_id | num | 直播间id | 必要 | |
| title | str | 直播间标题 | 非必要 | |
| area_id | num | 直播分区id | 非必要 | |
| csrf | str | CSRF Token | 必要 | |

---

## 📺 直播间弹幕监控

### 1. 获取信息流认证秘钥
**接口地址：** `https://api.live.bilibili.com/xlive/web-room/v1/index/getDanmuInfo`  
**请求方式：** GET  
**认证方式：** Cookie(SESSDATA)  
**鉴权方式：** Wbi 签名, Cookie中的`buvid3`不为空  

**URL参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| id | num | 直播间真实id | 必要 | |
| type | num | (?) | 非必要 | 作用尚不明确 |
| web_location | str | (?) | 非必要 | 作用尚不明确 |
| w_rid | str | Wbi 签名 | 必要 | 详见 Wbi 签名文档 |
| wts | num | 当前时间戳 | 必要 | 详见 Wbi 签名文档 |

**响应数据：**
```json
{
  "code": 0,
  "message": "0",
  "ttl": 1,
  "data": {
    "group": "live",
    "business_id": 0,
    "refresh_row_factor": 0.125,
    "refresh_rate": 100,
    "max_delay": 5000,
    "token": "认证token",
    "host_list": [
      {
        "host": "服务器域名",
        "port": TCP端口,
        "wss_port": WSS端口,
        "ws_port": WS端口
      }
    ]
  }
}
```

### 2. WebSocket连接格式
**连接地址：** `wss://broadcastlv.chat.bilibili.com:443/sub`  
**协议：** WebSocket  

**数据包格式：** 头部数据 + 正文数据  

**操作流程：**
1. 建立WebSocket连接
2. 发送认证包
3. 接收认证回复
4. 定时发送心跳包(30秒间隔)
5. 接收普通数据包

**主要消息类型：**
- `DANMU_MSG`: 弹幕消息
- `SEND_GIFT`: 礼物消息
- `COMBO_SEND`: 连击礼物
- `GUARD_BUY`: 上舰消息
- `USER_TOAST_MSG`: 续费消息
- `INTERACT_WORD`: 用户进入直播间
- `ONLINE_RANK_COUNT`: 高能榜数量
- `ONLINE_RANK_V2`: 高能榜变化
- `STOP_LIVE_ROOM_LIST`: 停止直播
- `PREPARING`: 直播准备中
- `LIVE`: 开始直播
- `ROOM_REAL_TIME_MESSAGE_UPDATE`: 粉丝数等更新

---

## 💬 发送消息相关

### 1. 发送直播弹幕
**接口地址：** `https://api.live.bilibili.com/msg/send`  
**请求方式：** POST  
**认证方式：** Cookie (SESSDATA)  
**鉴权方式：** Cookie中`bili_jct`的值正确并与`csrf`相同  

**URL参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| w_rid | str | wbi签名 | 非必要 | 不强制需要 |
| wts | num | Unix 秒时间戳 | 非必要 | 不强制需要 |

**正文参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| csrf | str | CSRF Token | 必要 | 位于cookie |
| roomid | num | 直播间id | 必要 | |
| msg | str | 弹幕内容 | 必要 | |
| rnd | num | 当前 Unix 秒时间戳 | 必要 | |
| fontsize | num | 字体大小 | 必要 | 默认为25 |
| color | num | 十进制颜色值 | 必要 | 实际无效果 |
| mode | num | 展示模式 | 非必要 | 默认为1 |
| bubble | num | (?) | 非必要 | 值为0 |
| room_type | num | (?) | 非必要 | 0 |
| jumpfrom | num | (?) | 非必要 | 0 |
| reply_mid | num | 要"@"的用户mid | 非必要 | 默认为0 |
| reply_attr | num | (?) | 非必要 | 0 |
| reply_uname | str | 要"@"的用户名称 | 非必要 | 默认为"" |
| replay_dmid | str | 要回复的弹幕id | 非必要 | 默认为"" |
| statistics | str | (?) | 非必要 | {"appId":100,"platform":5} |
| csrf_token | str | 同csrf | 非必要 | |

**响应状态码：**
- `0`: 成功
- `-101`: 账号未登录
- `-111`: csrf 校验失败
- `-400`: 请求错误
- `1003212`: 超出限制长度
- `10031`: 发送频率过快

### 2. 获取当前用户对应直播间可发弹幕配置
**接口地址：** `https://api.live.bilibili.com/xlive/web-room/v1/dM/GetDMConfigByGroup`  
**请求方式：** GET  
**认证方式：** Cookie (SESSDATA)  

**URL参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| room_id | num | 直播间id | 必要 | |
| web_location | str | (?) | 非必要 | 作用尚不明确 |
| w_rid | str | wbi签名 | 非必要 | 不强制需要 |
| wts | num | 秒时间戳 | 非必要 | 不强制需要 |

### 3. 发送视频弹幕
**接口地址：** `https://api.bilibili.com/x/v2/dm/post`  
**请求方式：** POST  
**认证方式：** Cookie（SESSDATA）或APP  

**URL参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| web_location | str | 普通视频: 1315873 | 不必要 | |
| csrf | str | CSRF Token | Cookie方式必要 | 即Cookie中bili_jct |
| w_rid | str | Wbi 签名 | 必要 | 参见 Wbi 签名文档 |
| wts | str | UNIX 秒级时间戳 | 必要 | 参见 Wbi 签名文档 |

**正文参数：**
| 参数名 | 类型 | 内容 | 必要性 | 备注 |
|--------|------|------|--------|------|
| type | num | 弹幕类型 | 必要 | 1:视频弹幕 2:漫画弹幕 |
| oid | num | 弹幕所属对象id | 必要 | 视频:cid 漫画:漫画id |
| msg | str | 弹幕内容 | 必要 | |
| aid | num | 稿件avid | 必要 | |
| progress | num | 弹幕出现时间 | 必要 | 毫秒 |
| color | num | 弹幕颜色 | 必要 | 十进制RGB888值 |
| fontsize | num | 弹幕字号 | 必要 | 12:非常小 16:特小 18:小 25:中 36:大 45:很大 64:特别大 |
| pool | num | 弹幕池 | 必要 | 0:普通池 1:字幕池 2:特殊池 |
| mode | num | 弹幕类型 | 必要 | 1-3:普通弹幕 4:底部弹幕 5:顶部弹幕 6:逆向弹幕 7:高级弹幕 8:代码弹幕 9:BAS弹幕 |
| rnd | num | 时间戳 | 必要 | 发送时的时间戳 |
| plat | num | 平台 | 必要 | 1:web端 |

### 4. 发送私信（web端）
**接口地址：** `https://api.vc.bilibili.com/web_im/v1/web_im/send_msg`  
**请求方式：** POST  
**认证方式：** Cookie（SESSDATA）  
**鉴权方式：** Wbi 签名  

**仅支持发送 `msg[msg_type]` 为 `1`、`2` 或 `5` 的私信**

---

## 📝 备注说明

1. **认证方式说明：**
   - Cookie（SESSDATA）：需要登录后的SESSDATA cookie
   - Wbi 签名：需要按照B站Wbi签名算法生成签名
   - CSRF Token：即Cookie中的bili_jct值

2. **常用状态码：**
   - `0`: 成功
   - `-101`: 账号未登录
   - `-111`: csrf校验失败
   - `-400`: 请求错误
   - `-404`: 无此项
   - `-500`: 服务器错误

3. **时间戳格式：**
   - 大部分接口使用Unix秒级时间戳
   - 弹幕相关接口部分使用毫秒级时间戳

4. **直播间ID说明：**
   - 短号：用户自定义的短直播间号
   - 长号：系统分配的真实直播间ID
   - 大部分接口需要使用真实ID

---

*整理时间：2025-01-20*  
*数据来源：bilibili-API-collect 项目文档*
